#ifndef _WEB_SOCKET_H_
#define _WEB_SOCKET_H_

#include <string>
#include <functional>

class WebSocket {
public:
    virtual ~WebSocket() = default;
    
    virtual void SetHeader(const char* key, const char* value) = 0;
    virtual bool Connect(const char* url) = 0;
    virtual bool IsConnected() const = 0;
    virtual bool Send(const std::string& text) = 0;
    virtual bool Send(const void* data, size_t len, bool binary = false) = 0;
    virtual void OnData(std::function<void(const char* data, size_t len, bool binary)> callback) = 0;
    virtual void OnDisconnected(std::function<void()> callback) = 0;
};

#endif // _WEB_SOCKET_H_ 