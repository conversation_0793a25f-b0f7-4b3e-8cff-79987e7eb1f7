#ifndef _HTTP_H_
#define _HTTP_H_

#include <string>

class Http {
public:
    virtual ~Http() = default;
    
    virtual void SetHeader(const char* key, const char* value) = 0;
    virtual bool Open(const char* method, const std::string& url) = 0;
    virtual void Write(const char* data, size_t len) = 0;
    virtual int GetStatusCode() = 0;
    virtual std::string ReadAll() = 0;
    virtual void Close() = 0;
};

#endif // _HTTP_H_ 