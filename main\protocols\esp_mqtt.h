#ifndef _ESP_MQTT_H_
#define _ESP_MQTT_H_

#include "mqtt.h"
#include <ml307_mqtt.h>
#include <string>
#include <functional>

class EspMqtt : public Mqtt {
public:
    EspMqtt();
    virtual ~EspMqtt();
    
    bool Connect(const std::string broker_address, int broker_port, const std::string client_id, const std::string username, const std::string password) override;
    void Disconnect() override;
    bool Publish(const std::string topic, const std::string payload, int qos = 0) override;
    bool Subscribe(const std::string topic, int qos = 0) override;
    bool Unsubscribe(const std::string topic) override;
    bool IsConnected() override;

private:
    bool connected_ = false;
    int keep_alive_ = 60;
    std::function<void(const std::string& topic, const std::string& payload)> message_callback_;
    std::function<void()> disconnected_callback_;
    // 这里应该有实际的 MQTT 客户端实例，例如 esp-mqtt 库的客户端
    // 为简化示例，这里省略
};

#endif // _ESP_MQTT_H_ 