#ifndef _ESP_MQTT_H_
#define _ESP_MQTT_H_

#include "mqtt.h"
#include <string>
#include <functional>

class EspMqtt : public IMqtt {
public:
    EspMqtt();
    virtual ~EspMqtt();
    
    void SetKeepAlive(int seconds) override;
    bool Connect(const std::string& broker, int port, const std::string& client_id, 
                const std::string& username = "", const std::string& password = "") override;
    bool IsConnected() const override;
    bool Publish(const std::string& topic, const std::string& payload) override;
    bool Subscribe(const std::string& topic) override;
    void OnMessage(std::function<void(const std::string& topic, const std::string& payload)> callback) override;
    void OnDisconnected(std::function<void()> callback) override;

private:
    bool connected_ = false;
    int keep_alive_ = 60;
    std::function<void(const std::string& topic, const std::string& payload)> message_callback_;
    std::function<void()> disconnected_callback_;
    // 这里应该有实际的 MQTT 客户端实例，例如 esp-mqtt 库的客户端
    // 为简化示例，这里省略
};

#endif // _ESP_MQTT_H_ 