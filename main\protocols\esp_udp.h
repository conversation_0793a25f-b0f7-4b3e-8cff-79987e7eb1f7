#ifndef _ESP_UDP_H_
#define _ESP_UDP_H_

#include "udp.h"
#include <ml307_udp.h>
#include <string>
#include <functional>

class EspUdp : public Udp {
public:
    EspUdp();
    virtual ~EspUdp();
    
    bool Connect(const std::string& host, int port) override;
    void Disconnect() override;
    int Send(const std::string& data) override;
    void OnMessage(std::function<void(const std::string& data)> callback) override;

private:
    bool connected_ = false;
    std::string host_;
    int port_ = 0;
    std::function<void(const std::string& data)> message_callback_;
    // 这里应该有实际的 UDP socket 实例
    // 为简化示例，这里省略
};

#endif // _ESP_UDP_H_ 