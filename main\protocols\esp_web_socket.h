#ifndef _ESP_WEB_SOCKET_H_
#define _ESP_WEB_SOCKET_H_

#include "web_socket.h"
#include <string>
#include <functional>

class Transport;

class EspWebSocket : public WebSocket {
public:
    EspWebSocket(Transport* transport);
    virtual ~EspWebSocket();
    
    void SetHeader(const char* key, const char* value) override;
    bool Connect(const char* url) override;
    bool IsConnected() const override;
    bool Send(const std::string& text) override;
    bool Send(const void* data, size_t len, bool binary = false) override;
    void OnData(std::function<void(const char* data, size_t len, bool binary)> callback) override;
    void OnDisconnected(std::function<void()> callback) override;

private:
    Transport* transport_;
    bool connected_ = false;
    std::function<void(const char* data, size_t len, bool binary)> data_callback_;
    std::function<void()> disconnected_callback_;
    std::map<std::string, std::string> headers_;
};

#endif // _ESP_WEB_SOCKET_H_ 