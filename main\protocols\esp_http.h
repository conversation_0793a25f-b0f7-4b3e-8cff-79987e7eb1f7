#ifndef _ESP_HTTP_H_
#define _ESP_HTTP_H_

#include "http.h"
#include <string>
#include <map>

class EspHttp : public Http {
public:
    EspHttp();
    virtual ~EspHttp();
    
    void SetHeader(const char* key, const char* value) override;
    bool Open(const char* method, const std::string& url) override;
    void Write(const char* data, size_t len) override;
    int GetStatusCode() override;
    std::string ReadAll() override;
    void Close() override;

private:
    std::map<std::string, std::string> headers_;
    bool connected_ = false;
    int status_code_ = 0;
    std::string response_body_;
    // 这里应该有实际的 HTTP 客户端实例，例如 esp-http-client
    // 为简化示例，这里省略
};

#endif // _ESP_HTTP_H_ 