#include "esp_web_socket.h"
#include <esp_log.h>
#include <map>

// 这里假设 Transport 类是一个已经存在的类，用于处理底层通信
// 实际实现可能需要根据项目中 Transport 类的实际接口进行调整
class Transport {
public:
    virtual ~Transport() = default;
    virtual bool Connect(const char* url) = 0;
    virtual bool IsConnected() const = 0;
    virtual bool Send(const void* data, size_t len) = 0;
    virtual void OnData(std::function<void(const char* data, size_t len)> callback) = 0;
    virtual void OnDisconnected(std::function<void()> callback) = 0;
    virtual void SetHeader(const char* key, const char* value) = 0;
};

#define TAG "EspWebSocket"

EspWebSocket::EspWebSocket(Transport* transport) : transport_(transport) {
}

EspWebSocket::~EspWebSocket() {
    if (transport_) {
        delete transport_;
        transport_ = nullptr;
    }
}

void EspWebSocket::SetHeader(const char* key, const char* value) {
    if (transport_) {
        transport_->SetHeader(key, value);
    }
}

bool EspWebSocket::Connect(const char* url) {
    if (!transport_) {
        ESP_LOGE(TAG, "Transport is null");
        return false;
    }
    
    connected_ = transport_->Connect(url);
    
    if (connected_) {
        transport_->OnData([this](const char* data, size_t len) {
            if (data_callback_) {
                // 这里简化处理，假设所有数据都是文本
                data_callback_(data, len, false);
            }
        });
        
        transport_->OnDisconnected([this]() {
            connected_ = false;
            if (disconnected_callback_) {
                disconnected_callback_();
            }
        });
    }
    
    return connected_;
}

bool EspWebSocket::IsConnected() const {
    return transport_ && transport_->IsConnected();
}

bool EspWebSocket::Send(const std::string& text) {
    if (!transport_ || !connected_) {
        return false;
    }
    return transport_->Send(text.c_str(), text.size());
}

bool EspWebSocket::Send(const void* data, size_t len, bool binary) {
    if (!transport_ || !connected_) {
        return false;
    }
    // 这里简化处理，不区分二进制和文本
    return transport_->Send(data, len);
}

void EspWebSocket::OnData(std::function<void(const char* data, size_t len, bool binary)> callback) {
    data_callback_ = callback;
}

void EspWebSocket::OnDisconnected(std::function<void()> callback) {
    disconnected_callback_ = callback;
} 