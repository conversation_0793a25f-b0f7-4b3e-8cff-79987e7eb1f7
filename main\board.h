#ifndef BOARD_H
#define BOARD_H

#include <string>
#include "audio_codec.h"
#include "led.h"
#include "display.h"
#include "backlight.h"
#include "camera.h"
#include <cJSON.h>

class Http;
class WebSocket;
class IMqtt;
class IUdp;

class Board {
private:
    std::string uuid_;
    std::string GenerateUuid();

public:
    Board();
    static Board& GetInstance();

    virtual ~Board() = default;

    // 如果有camera，则返回camera，否则返回null
    virtual Camera* GetCamera() { return nullptr; }

    virtual std::string GetDeviceStatusJson();
    virtual std::string GetBoardJson();
    virtual std::string GetJson();

    virtual bool GetBatteryLevel(int &level, bool& charging, bool& discharging);
    virtual bool GetTemperature(float& esp32temp);

    virtual AudioCodec* GetAudioCodec() = 0;
    virtual Display* GetDisplay() = 0;
    virtual Backlight* GetBacklight() { return nullptr; }
    virtual Led* GetLed() = 0;
    virtual const char* GetTheme() { return "dark"; }
    
    // 获取设备唯一标识
    std::string GetUuid() const { return uuid_; }
    
    // 网络相关方法
    virtual std::string GetBoardType() { return "board"; }
    virtual void StartNetwork() {}
    virtual Http* CreateHttp() { return nullptr; }
    virtual WebSocket* CreateWebSocket() { return nullptr; }
    virtual IMqtt* CreateMqtt() { return nullptr; }
    virtual IUdp* CreateUdp() { return nullptr; }
    virtual const char* GetNetworkStateIcon() { return ""; }
    virtual void SetPowerSaveMode(bool enabled) {}
    
    // 音频输出模式相关方法
    virtual bool SupportAudioModeSwitch() { return false; }
    virtual bool IsInSpeakerMode() { return true; }
    virtual void SetAudioMode(bool speaker_mode) {}

    virtual void InitializeCamera() {}
};

#define DECLARE_BOARD(board_class) \
  Board& Board::GetInstance() {    \
    static board_class instance;   \
    return instance;               \
  }

#endif 