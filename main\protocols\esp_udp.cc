#include "esp_udp.h"
#include <esp_log.h>

#define TAG "EspUdp"

EspUdp::EspUdp() {
    // 初始化 UDP socket
}

EspUdp::~EspUdp() {
    // 清理资源
}

bool EspUdp::Connect(const std::string& host, int port) {
    host_ = host;
    port_ = port;
    
    ESP_LOGI(TAG, "Connecting to UDP server: %s:%d", host.c_str(), port);
    
    // 这里应该实现实际的 UDP socket 连接逻辑
    // 为简化示例，这里假设连接成功
    connected_ = true;
    
    return connected_;
}

int EspUdp::Send(const std::string& data) {
    if (!connected_) {
        ESP_LOGE(TAG, "Not connected to UDP server");
        return -1;
    }
    
    ESP_LOGD(TAG, "Sending UDP data, size: %d", data.size());
    
    // 这里应该实现实际的 UDP 发送逻辑
    // 为简化示例，这里假设发送成功，返回发送的字节数
    return data.size();
}

void EspUdp::OnMessage(std::function<void(const std::string& data)> callback) {
    message_callback_ = callback;
} 