#ifndef _MQTT_H_
#define _MQTT_H_

#include <string>
#include <functional>

class IMqtt {
public:
    virtual ~IMqtt() = default;
    
    virtual void SetKeepAlive(int seconds) = 0;
    virtual bool Connect(const std::string& broker, int port, const std::string& client_id, 
                const std::string& username = "", const std::string& password = "") = 0;
    virtual bool IsConnected() const = 0;
    virtual bool Publish(const std::string& topic, const std::string& payload) = 0;
    virtual bool Subscribe(const std::string& topic) = 0;
    virtual void OnMessage(std::function<void(const std::string& topic, const std::string& payload)> callback) = 0;
    virtual void OnDisconnected(std::function<void()> callback) = 0;
};

#endif // _MQTT_H_ 